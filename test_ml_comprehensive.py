#!/usr/bin/env python
import os
import sys
import django

# Set up Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ttuguard_temp.settings')
django.setup()

from ml_models.ml_utils import classify_review, get_active_model
from ml_models.models import MLModel
from reviews.models import NewReview
import pandas as pd

print("=== COMPREHENSIVE ML MODEL TESTING ===")

# 1. Check active model
print("\n1. CHECKING ACTIVE MODEL:")
try:
    active_models = MLModel.objects.filter(is_active=True)
    print(f"   Active models count: {active_models.count()}")
    
    for model in active_models:
        print(f"   - Model: {model.name} v{model.version}")
        print(f"     File: {model.file_path}")
        print(f"     Accuracy: {model.accuracy}")
        print(f"     File exists: {os.path.exists(model.file_path)}")
        
    detector = get_active_model()
    print(f"   Detector loaded: {detector is not None}")
    if detector:
        print(f"   Model type: {type(detector.model)}")
        
except Exception as e:
    print(f"   ERROR: {e}")
    import traceback
    traceback.print_exc()

# 2. Test with known examples from training data
print("\n2. TESTING WITH TRAINING DATA EXAMPLES:")
try:
    # Load some examples from training data
    df = pd.read_csv('scraped_data/kaggle_dataset.csv')
    
    # Get some clear fake examples (label = 1)
    fake_examples = df[df['fake'] == 1]['text'].head(3).tolist()
    # Get some clear real examples (label = 0)  
    real_examples = df[df['fake'] == 0]['text'].head(3).tolist()
    
    print("   FAKE EXAMPLES (should predict 'fake'):")
    for i, text in enumerate(fake_examples, 1):
        result = classify_review(text)
        print(f"   {i}. Text: {text[:60]}...")
        print(f"      Predicted: {result['prediction_label']} (confidence: {result['confidence_score']:.4f})")
        print(f"      ✓ CORRECT" if result['prediction_label'] == 'fake' else "✗ WRONG")
        print()
    
    print("   REAL EXAMPLES (should predict 'real'):")
    for i, text in enumerate(real_examples, 1):
        result = classify_review(text)
        print(f"   {i}. Text: {text[:60]}...")
        print(f"      Predicted: {result['prediction_label']} (confidence: {result['confidence_score']:.4f})")
        print(f"      ✓ CORRECT" if result['prediction_label'] == 'real' else "✗ WRONG")
        print()
        
except Exception as e:
    print(f"   ERROR: {e}")
    import traceback
    traceback.print_exc()

# 3. Test the user's specific example
print("\n3. TESTING USER'S SPECIFIC EXAMPLE:")
user_review = "I've been using the ClearBrew for a little over two weeks now, and it's become a regular part of my morning routine. The setup was super simple, and the cold brew comes out incredibly smooth with zero bitterness. The design is sleek and doesn't take up much counter space, which is a big plus for my small kitchen. I've stopped buying coffee outside — this honestly saves me money every week. Totally worth the investment."

try:
    result = classify_review(user_review)
    print(f"   Text: {user_review[:60]}...")
    print(f"   Predicted: {result['prediction_label']}")
    print(f"   Confidence: {result['confidence_score']:.4f} ({result['confidence_score']*100:.2f}%)")
    
    # Check if it would be flagged
    would_be_flagged = result['prediction_label'] == 'fake' and result['confidence_score'] >= 0.7
    print(f"   Would be flagged: {would_be_flagged}")
    
except Exception as e:
    print(f"   ERROR: {e}")
    import traceback
    traceback.print_exc()

# 4. Check existing reviews in database
print("\n4. CHECKING EXISTING REVIEWS IN DATABASE:")
try:
    total_reviews = NewReview.objects.count()
    fake_reviews = NewReview.objects.filter(prediction='fake').count()
    real_reviews = NewReview.objects.filter(prediction='real').count()
    no_prediction = NewReview.objects.filter(prediction__isnull=True).count()
    
    print(f"   Total reviews: {total_reviews}")
    print(f"   Fake predictions: {fake_reviews}")
    print(f"   Real predictions: {real_reviews}")
    print(f"   No prediction: {no_prediction}")
    
    if total_reviews > 0:
        print(f"   Fake percentage: {(fake_reviews/total_reviews)*100:.1f}%")
        print(f"   Real percentage: {(real_reviews/total_reviews)*100:.1f}%")
        
        # Show some recent reviews
        recent_reviews = NewReview.objects.all().order_by('-created_at')[:5]
        print(f"\n   RECENT REVIEWS:")
        for i, review in enumerate(recent_reviews, 1):
            print(f"   {i}. Text: {review.review_text[:50]}...")
            print(f"      Prediction: {review.prediction}")
            print(f"      Confidence: {review.confidence}")
            print(f"      Flagged: {review.is_flagged}")
            print()
            
except Exception as e:
    print(f"   ERROR: {e}")
    import traceback
    traceback.print_exc()

# 5. Test model performance on sample data
print("\n5. TESTING MODEL PERFORMANCE:")
try:
    # Test with obvious fake patterns
    obvious_fake_tests = [
        "AMAZING PRODUCT!!!! Changed my life overnight! Will buy again and again!!!!",
        "I ordered this product and received it within 5 minutes! The shipping was INCREDIBLE and the product is PERFECT!",
        "This product cured all my problems overnight! Miracle product! Buy it now! 100% perfect in every way!",
        "BEST PRODUCT EVER!!!! 100% PERFECT!!!! EVERYONE MUST BUY THIS NOW!!!!"
    ]
    
    # Test with obvious real patterns
    obvious_real_tests = [
        "The product works as described. Decent value for the price.",
        "It's okay. Nothing special but it works.",
        "Solid product with good features. A few minor issues but overall satisfied.",
        "Good quality, arrived on time. Would recommend."
    ]
    
    fake_correct = 0
    real_correct = 0
    
    print("   OBVIOUS FAKE TESTS:")
    for text in obvious_fake_tests:
        result = classify_review(text)
        is_correct = result['prediction_label'] == 'fake'
        if is_correct:
            fake_correct += 1
        print(f"   - {result['prediction_label']} ({result['confidence_score']:.3f}) {'✓' if is_correct else '✗'}")
    
    print("   OBVIOUS REAL TESTS:")
    for text in obvious_real_tests:
        result = classify_review(text)
        is_correct = result['prediction_label'] == 'real'
        if is_correct:
            real_correct += 1
        print(f"   - {result['prediction_label']} ({result['confidence_score']:.3f}) {'✓' if is_correct else '✗'}")
    
    print(f"\n   PERFORMANCE SUMMARY:")
    print(f"   Fake detection: {fake_correct}/{len(obvious_fake_tests)} ({(fake_correct/len(obvious_fake_tests))*100:.1f}%)")
    print(f"   Real detection: {real_correct}/{len(obvious_real_tests)} ({(real_correct/len(obvious_real_tests))*100:.1f}%)")
    
except Exception as e:
    print(f"   ERROR: {e}")
    import traceback
    traceback.print_exc()

print("\n=== TEST COMPLETE ===")
