from django.db import models
import uuid
from reviews.models import NewReview

class Payment(models.Model):
    """
    Model to store payment information for review verification
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    review = models.ForeignKey(NewReview, on_delete=models.CASCADE, related_name='payments', null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='GHS')
    reference = models.CharField(max_length=255, unique=True, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    verified = models.BooleanField(default=False)
    
    # Paystack fields
    paystack_reference = models.CharField(max_length=100, blank=True, null=True)
    paystack_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Status fields
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('completed', 'Completed'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ],
        default='pending'
    )
    
    # Metadata fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        review_id = self.review.id if self.review else 'None'
        reference = self.reference if self.reference else 'None'
        return f"Payment {reference} for Review {review_id}"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['review_id']),
            models.Index(fields=['status']),
        ]
