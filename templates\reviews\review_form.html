{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Submit Review | TTUGuard{% endblock %}

{% block page_title %}Submit Review{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Submit a Product Review</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.product_id.id_for_label }}" class="form-label">{{ form.product_id.label }}</label>
                        {{ form.product_id|add_class:"form-control" }}
                        {% if form.product_id.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.product_id.errors }}
                        </div>
                        {% endif %}
                        {% if form.product_id.help_text %}
                        <small class="form-text text-muted">{{ form.product_id.help_text }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.product_name.id_for_label }}" class="form-label">{{ form.product_name.label }}</label>
                        {{ form.product_name|add_class:"form-control" }}
                        {% if form.product_name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.product_name.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.review_text.id_for_label }}" class="form-label">{{ form.review_text.label }}</label>
                        {{ form.review_text|add_class:"form-control" }}
                        {% if form.review_text.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.review_text.errors }}
                        </div>
                        {% endif %}
                        {% if form.review_text.help_text %}
                        <small class="form-text text-muted">{{ form.review_text.help_text }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.rating.id_for_label }}" class="form-label">{{ form.rating.label }}</label>
                        <div class="star-rating">
                            <input type="radio" id="rating_5" name="{{ form.rating.name }}" value="5" {% if form.rating.value == '5' %}checked{% endif %} />
                            <label for="rating_5" title="5 stars">★</label>
                            
                            <input type="radio" id="rating_4" name="{{ form.rating.name }}" value="4" {% if form.rating.value == '4' %}checked{% endif %} />
                            <label for="rating_4" title="4 stars">★</label>
                            
                            <input type="radio" id="rating_3" name="{{ form.rating.name }}" value="3" {% if form.rating.value == '3' %}checked{% endif %} />
                            <label for="rating_3" title="3 stars">★</label>
                            
                            <input type="radio" id="rating_2" name="{{ form.rating.name }}" value="2" {% if form.rating.value == '2' %}checked{% endif %} />
                            <label for="rating_2" title="2 stars">★</label>
                            
                            <input type="radio" id="rating_1" name="{{ form.rating.name }}" value="1" {% if form.rating.value == '1' %}checked{% endif %} />
                            <label for="rating_1" title="1 star">★</label>
                        </div>
                        {% if form.rating.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.rating.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.author.id_for_label }}" class="form-label">{{ form.author.label }}</label>
                        {{ form.author|add_class:"form-control" }}
                        {% if form.author.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.author.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Your review will be automatically analyzed by our AI system to detect fake reviews.
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                        <a href="{% url 'reviews:review_list' %}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<style>
    .star-rating {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
        font-size: 1.5rem;
    }
    
    .star-rating input {
        display: none;
    }
    
    .star-rating label {
        color: #ddd;
        cursor: pointer;
        padding: 0 0.1em;
        transition: color 0.2s;
    }
    
    .star-rating label:hover,
    .star-rating label:hover ~ label,
    .star-rating input:checked ~ label {
        color: #ffc107;
    }
</style>
{% endblock %} 