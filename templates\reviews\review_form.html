{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Submit Review - ReviewMate{% endblock %}

{% block page_title %}Submit a Review{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Submit Your Review</h5>
                <small class="text-muted">Help others by sharing your honest experience</small>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <h6>Please correct the following errors:</h6>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <div>{{ field }}: {{ error }}</div>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.product_name.id_for_label }}" class="form-label">Product Name *</label>
                        {{ form.product_name|add_class:"form-control" }}
                        {% if form.product_name.help_text %}
                            <div class="form-text">{{ form.product_name.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.product_id.id_for_label }}" class="form-label">Product ID *</label>
                        {{ form.product_id|add_class:"form-control" }}
                        {% if form.product_id.help_text %}
                            <div class="form-text">{{ form.product_id.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.author.id_for_label }}" class="form-label">Your Name *</label>
                        {{ form.author|add_class:"form-control" }}
                        {% if form.author.help_text %}
                            <div class="form-text">{{ form.author.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.rating.id_for_label }}" class="form-label">Rating *</label>
                        {{ form.rating|add_class:"form-select" }}
                        {% if form.rating.help_text %}
                            <div class="form-text">{{ form.rating.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="{{ form.review_text.id_for_label }}" class="form-label">Review Text *</label>
                        {{ form.review_text|add_class:"form-control" }}
                        {% if form.review_text.help_text %}
                            <div class="form-text">{{ form.review_text.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'reviews:review_list' %}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mt-4">
            <div class="alert alert-info">
                <h6>How it works:</h6>
                <ol class="mb-0">
                    <li>Submit your review</li>
                    <li>Our AI analyzes it for authenticity</li>
                    <li>If flagged as potentially fake, you can verify it through payment</li>
                    <li>Verified reviews are marked as genuine</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}
