{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Submit Review | ReviewMate{% endblock %}

{% block page_title %}Submit Review{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Submit a Product Review</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.product_id.id_for_label }}" class="form-label">Product ID</label>
                        {{ form.product_id|add_class:"form-control" }}
                        {% if form.product_id.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.product_id.errors }}
                        </div>
                        {% endif %}
                        {% if form.product_id.help_text %}
                        <small class="form-text text-muted">{{ form.product_id.help_text }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.product_name.id_for_label }}" class="form-label">Product Name</label>
                        {{ form.product_name|add_class:"form-control" }}
                        {% if form.product_name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.product_name.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.rating.id_for_label }}" class="form-label">Rating</label>
                        {{ form.rating|add_class:"form-select" }}
                        {% if form.rating.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.rating.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.review_text.id_for_label }}" class="form-label">Review Text</label>
                        {{ form.review_text|add_class:"form-control" }}
                        {% if form.review_text.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.review_text.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'reviews:review_list' %}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
