{% extends 'base.html' %} {% block title %}Review Detail - ReviewMate{% endblock
%} {% block page_title %}Review Detail{% endblock %} {% block content %}
<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="mb-0">{{ review.product_name }}</h5>
    <div>
      {% if review.is_verified %}
      <span class="badge verified-badge">Verified</span>
      {% elif review.prediction == 'fake' %}
      <span class="badge fake-badge">Potentially Fake</span>
      {% elif review.prediction == 'real' %}
      <span class="badge real-badge">Real</span>
      {% endif %}
    </div>
  </div>
  <div class="card-body">
    <div class="mb-3">
      <h6>Rating</h6>
      <div class="mb-2">
        {% for i in "12345" %} {% if forloop.counter <= review.rating %}
        <span class="text-warning">★</span>
        {% else %}
        <span class="text-secondary">★</span>
        {% endif %} {% endfor %}
        <span class="ms-2">{{ review.rating }}/5</span>
      </div>
    </div>

    <div class="mb-3">
      <h6>Review</h6>
      <p>{{ review.review_text }}</p>
    </div>

    <div class="mb-3">
      <h6>Date Posted</h6>
      <p>{{ review.created_at|date:"F j, Y" }}</p>
    </div>

    {% if review.prediction %}
    <div class="mb-3">
      <h6>AI Analysis</h6>
      <div
        class="alert {% if review.is_verified %}alert-success{% elif review.prediction == 'fake' %}alert-danger{% else %}alert-success{% endif %}"
      >
        <p>
          <strong>Prediction:</strong>
          {% if review.is_verified %} This review has been verified, hence, it
          is real {% elif review.prediction == 'fake' %} This review is likely
          fake {% else %} This review appears to be genuine {% endif %}
        </p>
        {% if review.confidence and not review.is_verified %}
        <p>
          <strong>Confidence:</strong> {% widthratio review.confidence 1 100 %}%
        </p>
        {% endif %}
      </div>
    </div>
    {% endif %} {% if review.prediction == 'fake' and not review.is_verified %}
    <div class="alert alert-warning">
      <h5 class="alert-heading">Verify this review</h5>
      <p>
        Our AI system has flagged this review as potentially fake. If you
        believe this is a genuine review, you can verify it by paying a small
        fee.
      </p>
      <hr />
      <div class="d-grid gap-2">
        <a
          href="{% url 'payments:payment_page' review_id=review.id %}"
          class="btn btn-primary"
        >
          Verify Review
        </a>
      </div>
    </div>
    {% endif %}
  </div>
  <div class="card-footer">
    <div class="d-flex justify-content-between">
      <a
        href="{% url 'reviews:review_list' %}"
        class="btn btn-outline-secondary"
      >
        Back to Reviews
      </a>
      {% if user.is_staff %}
      <div>
        <a
          href="{% url 'admin:reviews_newreview_change' review.id %}"
          class="btn btn-outline-primary"
        >
          Edit in Admin
        </a>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
{% endblock %}
