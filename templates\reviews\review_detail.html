{% extends 'base.html' %}

{% block title %}Review Detail - ReviewMate{% endblock %}

{% block page_title %}Review Detail{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ review.product_name }}</h5>
        <div>
            {% if review.is_verified %}
                <span class="badge bg-success">Verified</span>
            {% elif review.prediction == 'fake' %}
                <span class="badge bg-danger">Potentially Fake</span>
            {% elif review.prediction == 'real' %}
                <span class="badge bg-success">Real</span>
            {% else %}
                <span class="badge bg-secondary">Analyzing...</span>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        {% for i in "12345" %}
                            {% if forloop.counter <= review.rating %}
                                <span class="text-warning fs-5">★</span>
                            {% else %}
                                <span class="text-secondary fs-5">★</span>
                            {% endif %}
                        {% endfor %}
                        <span class="ms-2 fw-bold">{{ review.rating }}/5</span>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6>Review Text:</h6>
                    <p class="lead">{{ review.review_text }}</p>
                </div>
                
                <div class="row">
                    <div class="col-sm-6">
                        <p><strong>Product ID:</strong> {{ review.product_id }}</p>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>Date:</strong> {{ review.created_at|date:"F j, Y" }}</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                {% if review.prediction %}
                <div class="card bg-light">
                    <div class="card-header">
                        <h6 class="mb-0">AI Analysis</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">
                            <strong>Prediction:</strong>
                            {% if review.is_verified %}
                                This review is verified as real
                            {% elif review.prediction == 'fake' %}
                                This review is likely fake
                            {% else %}
                                This review appears to be genuine
                            {% endif %}
                        </p>
                        {% if review.confidence and not review.is_verified %}
                        <p>
                          <strong>Confidence:</strong> {% widthratio review.confidence 1 100 %}%
                        </p>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
                
                {% if review.prediction == 'fake' and not review.is_verified %}
                <div class="mt-3">
                    <div class="alert alert-warning">
                        <h6>Verification Required</h6>
                        <p class="mb-2">This review has been flagged as potentially fake. You can verify it through our payment system.</p>
                        <a href="{% url 'payments:payment_page' review_id=review.id %}" class="btn btn-warning">Verify Review</a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex justify-content-between">
            <a href="{% url 'reviews:review_list' %}" class="btn btn-secondary">← Back to Reviews</a>
            {% if review.prediction == 'fake' and not review.is_verified %}
                <a href="{% url 'payments:payment_page' review_id=review.id %}" class="btn btn-warning">Verify Review</a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
