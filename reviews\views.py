from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.conf import settings
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponseRedirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.http import Http404
from django.db.models import Q
from django.utils import timezone
import json
import uuid
import os
from datetime import datetime

from .models import NewReview
from .forms import ReviewForm
from ml_models.ml_utils import classify_review, predict_review
from .scraper_new import scrape_reviews, scrape_reviews_from_source

class ReviewListView(ListView):
    """
    View to list all reviews
    """
    model = NewReview
    template_name = 'reviews/review_list.html'
    context_object_name = 'reviews'
    paginate_by = 10

    def get_queryset(self):
        """
        Filter reviews based on query parameters
        """
        queryset = super().get_queryset().order_by('-created_at')

        # Apply filters from GET parameters
        product_id = self.request.GET.get('product_id', '')
        prediction = self.request.GET.get('prediction', '')
        is_flagged = self.request.GET.get('is_flagged', '')
        is_verified = self.request.GET.get('is_verified', '')

        if product_id:
            queryset = queryset.filter(product_id__icontains=product_id)

        if prediction:
            queryset = queryset.filter(prediction=prediction)

        if is_flagged:
            if is_flagged.lower() == 'true':
                queryset = queryset.filter(is_flagged=True)
            elif is_flagged.lower() == 'false':
                queryset = queryset.filter(is_flagged=False)

        if is_verified:
            if is_verified.lower() == 'true':
                queryset = queryset.filter(is_verified=True)
            elif is_verified.lower() == 'false':
                queryset = queryset.filter(is_verified=False)

        return queryset

    def get_context_data(self, **kwargs):
        """
        Add filter parameters to context
        """
        context = super().get_context_data(**kwargs)
        context['filters'] = {
            'product_id': self.request.GET.get('product_id', ''),
            'prediction': self.request.GET.get('prediction', ''),
            'is_flagged': self.request.GET.get('is_flagged', ''),
            'is_verified': self.request.GET.get('is_verified', ''),
        }
        return context


class ReviewDetailView(DetailView):
    """
    View to show details of a review
    """
    model = NewReview
    template_name = 'reviews/review_detail.html'
    context_object_name = 'review'


class ReviewCreateView(CreateView):
    """
    View to create a new review
    """
    model = NewReview
    form_class = ReviewForm
    template_name = 'reviews/review_form.html'
    success_url = reverse_lazy('reviews:review_list')

    def form_valid(self, form):
        """
        Process form submission and classify review
        """
        try:
            # Save the review but don't commit to DB yet
            review = form.save(commit=False)

            # Set source as user-submitted
            review.source = 'user'

            # Use ML model to predict if review is fake or real
            result = classify_review(review.review_text)
            review.prediction = result['prediction_label']
            review.confidence = result['confidence_score']

            # Flag review if predicted as fake with high confidence
            if review.prediction == 'fake' and review.confidence >= 0.7:
                review.is_flagged = True

            # Save the review
            review.save()

            messages.success(self.request, 'Review submitted successfully.')

            # If review is flagged as fake, redirect to payment page
            if review.is_flagged:
                messages.warning(self.request, 'This review has been flagged as potentially fake. You can verify it by making a payment.')
                return redirect('payments:payment_page', review_id=review.id)

            return super().form_valid(form)
        except Exception as e:
            # Add error message and re-render form
            messages.error(self.request, f'Error processing review: {str(e)}')
            return self.form_invalid(form)


@login_required
@user_passes_test(lambda u: u.is_staff)
def admin_dashboard(request):
    """
    Admin dashboard for review statistics
    """
    # Get counts
    total_reviews = NewReview.objects.count()
    fake_reviews = NewReview.objects.filter(prediction='fake').count()
    real_reviews = NewReview.objects.filter(prediction='real').count()
    flagged_reviews = NewReview.objects.filter(is_flagged=True).count()
    verified_reviews = NewReview.objects.filter(is_verified=True).count()

    # Get recent reviews
    recent_reviews = NewReview.objects.all().order_by('-created_at')[:10]

    context = {
        'total_reviews': total_reviews,
        'fake_reviews': fake_reviews,
        'real_reviews': real_reviews,
        'flagged_reviews': flagged_reviews,
        'verified_reviews': verified_reviews,
        'recent_reviews': recent_reviews,
    }

    return render(request, 'reviews/admin_dashboard.html', context)


@login_required
@user_passes_test(lambda u: u.is_staff)
def override_review(request, pk):
    """
    Override the ML classification of a review
    """
    review = get_object_or_404(NewReview, pk=pk)

    # Toggle prediction label
    if review.prediction == 'fake':
        review.prediction = 'real'
        review.is_flagged = False
        messages.success(request, 'Review marked as real.')
    else:
        review.prediction = 'fake'
        review.is_flagged = True
        messages.success(request, 'Review marked as fake.')

    review.save()

    # Redirect back to the review detail page
    return redirect('reviews:review_detail', pk=review.id)


@csrf_exempt
@require_POST
def api_submit_review(request):
    """
    API endpoint for submitting reviews
    """
    try:
        # Parse JSON data
        data = json.loads(request.body)

        # Extract required fields
        product_id = data.get('product_id')
        review_text = data.get('review_text')
        rating = data.get('rating')

        # Validate required fields
        if not product_id or not review_text or not rating:
            return JsonResponse({'error': 'Missing required fields'}, status=400)

        # Validate rating
        try:
            rating = int(rating)
            if rating < 1 or rating > 5:
                return JsonResponse({'error': 'Rating must be between 1 and 5'}, status=400)
        except ValueError:
            return JsonResponse({'error': 'Invalid rating'}, status=400)

        # Create user ID if provided
        user_id = data.get('user_id')
        if user_id:
            try:
                user_id = uuid.UUID(user_id)
            except ValueError:
                return JsonResponse({'error': 'Invalid user ID'}, status=400)

        # Classify review
        result = classify_review(review_text)

        # Create review
        review = NewReview.objects.create(
            product_id=product_id,
            review_text=review_text,
            rating=rating,
            user_id=user_id,
            source='api',
            prediction=result['prediction_label'],
            confidence=result['confidence_score'],
            is_flagged=(result['prediction_label'] == 'fake' and
                       result['confidence_score'] >= getattr(settings, 'CONFIDENCE_THRESHOLD', 0.7))
        )

        # Return success response
        return JsonResponse({
            'id': str(review.id),
            'prediction': review.prediction,
            'confidence': review.confidence,
            'is_flagged': review.is_flagged
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@user_passes_test(lambda u: u.is_staff)
def scrape_reviews_view(request):
    """
    View for scraping reviews from external sources
    """
    if request.method == 'POST':
        # Handle scraping form submission
        source = request.POST.get('source', '')
        product_id = request.POST.get('product_id', '')

        try:
            # Call scraper function
            count = scrape_reviews_from_source(source, product_id)
            messages.success(request, f'Successfully scraped {count} reviews.')
        except Exception as e:
            messages.error(request, f'Error scraping reviews: {str(e)}')

        return redirect('reviews:scrape_reviews')

    context = {
        'sources': ['amazon', 'walmart', 'target']  # Available sources
    }

    return render(request, 'reviews/scrape_reviews.html', context)