import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import logging
from datetime import datetime
from reviews.models import NewReview

logger = logging.getLogger(__name__)

class ReviewScraper:
    """
    Base class for review scrapers
    """
    def __init__(self, delay=1.0, jitter=0.5):
        """
        Initialize the scraper
        
        Args:
            delay: Base delay between requests in seconds
            jitter: Random jitter to add to delay
        """
        self.delay = delay
        self.jitter = jitter
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def _wait(self):
        """
        Wait between requests to avoid being blocked
        """
        time.sleep(self.delay + random.uniform(0, self.jitter))
    
    def scrape(self, url, max_pages=1):
        """
        Scrape reviews from the given URL
        
        Args:
            url: URL to scrape
            max_pages: Maximum number of pages to scrape
        
        Returns:
            List of review dictionaries
        """
        raise NotImplementedError("Subclasses must implement this method")


class YelpScraper(ReviewScraper):
    """
    Scraper for Yelp reviews
    """
    def scrape(self, url, max_pages=1):
        """
        Scrape reviews from Yelp
        
        Args:
            url: URL of the business page
            max_pages: Maximum number of pages to scrape
        
        Returns:
            List of review dictionaries
        """
        reviews = []
        current_page = 1
        
        while current_page <= max_pages:
            # Construct page URL
            page_url = url
            if current_page > 1:
                page_url = f"{url}?start={10 * (current_page - 1)}"
            
            try:
                # Get page content
                response = self.session.get(page_url)
                response.raise_for_status()
                
                # Parse HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find review containers
                review_elements = soup.select('div.review')
                
                if not review_elements:
                    logger.warning(f"No reviews found on page {current_page}")
                    break
                
                # Extract data from each review
                for review_element in review_elements:
                    try:
                        # Extract review text
                        review_text_element = review_element.select_one('p.comment__09f24__D0cxf')
                        review_text = review_text_element.text.strip() if review_text_element else ""
                        
                        # Extract rating
                        rating_element = review_element.select_one('[aria-label*="star rating"]')
                        rating = 0
                        if rating_element:
                            aria_label = rating_element.get('aria-label', '')
                            rating = int(aria_label.split()[0]) if aria_label else 0
                        
                        # Extract date
                        date_element = review_element.select_one('span.css-chan6m')
                        date_str = date_element.text.strip() if date_element else ""
                        
                        # Extract user info
                        user_element = review_element.select_one('a.css-1m051bw')
                        user_id = user_element.get('href', '').split('=')[-1] if user_element else ""
                        
                        # Create review object
                        review = {
                            'product_id': url.split('/')[-1],
                            'review_text': review_text,
                            'rating': rating,
                            'date': date_str,
                            'user_id': user_id,
                            'source': 'yelp'
                        }
                        
                        reviews.append(review)
                    except Exception as e:
                        logger.error(f"Error extracting review: {e}")
                
                # Wait before next request
                self._wait()
                current_page += 1
                
            except Exception as e:
                logger.error(f"Error scraping page {current_page}: {e}")
                break
        
        return reviews


class AmazonScraper(ReviewScraper):
    """
    Scraper for Amazon reviews
    """
    def scrape(self, url, max_pages=1):
        """
        Scrape reviews from Amazon
        
        Args:
            url: URL of the product page
            max_pages: Maximum number of pages to scrape
        
        Returns:
            List of review dictionaries
        """
        reviews = []
        current_page = 1
        
        # Extract product ID from URL
        product_id = url.split('/')[-1]
        if not product_id or '=' in product_id:
            product_id = url.split('dp/')[-1].split('/')[0]
        
        while current_page <= max_pages:
            # Construct review page URL
            page_url = f"https://www.amazon.com/product-reviews/{product_id}/ref=cm_cr_dp_d_show_all_btm?ie=UTF8&reviewerType=all_reviews&pageNumber={current_page}"
            
            try:
                # Get page content
                response = self.session.get(page_url)
                response.raise_for_status()
                
                # Parse HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find review containers
                review_elements = soup.select('div[data-hook="review"]')
                
                if not review_elements:
                    logger.warning(f"No reviews found on page {current_page}")
                    break
                
                # Extract data from each review
                for review_element in review_elements:
                    try:
                        # Extract review text
                        review_text_element = review_element.select_one('span[data-hook="review-body"]')
                        review_text = review_text_element.text.strip() if review_text_element else ""
                        
                        # Extract rating
                        rating_element = review_element.select_one('i[data-hook="review-star-rating"] span')
                        rating = 0
                        if rating_element:
                            rating_str = rating_element.text.strip().split()[0]
                            rating = int(float(rating_str)) if rating_str else 0
                        
                        # Extract date
                        date_element = review_element.select_one('span[data-hook="review-date"]')
                        date_str = date_element.text.strip() if date_element else ""
                        
                        # Extract user info
                        user_element = review_element.select_one('span.a-profile-name')
                        user_name = user_element.text.strip() if user_element else ""
                        
                        # Create review object
                        review = {
                            'product_id': product_id,
                            'review_text': review_text,
                            'rating': rating,
                            'date': date_str,
                            'user_id': user_name,  # Using name as ID since actual ID is not easily available
                            'source': 'amazon'
                        }
                        
                        reviews.append(review)
                    except Exception as e:
                        logger.error(f"Error extracting review: {e}")
                
                # Wait before next request
                self._wait()
                current_page += 1
                
            except Exception as e:
                logger.error(f"Error scraping page {current_page}: {e}")
                break
        
        return reviews


def save_reviews_to_csv(reviews, output_path):
    """
    Save scraped reviews to a CSV file
    
    Args:
        reviews: List of review dictionaries
        output_path: Path to save the CSV file
    """
    df = pd.DataFrame(reviews)
    df.to_csv(output_path, index=False)
    return df


def scrape_reviews(source, url, max_pages=1, output_path=None):
    """
    Scrape reviews from the specified source
    
    Args:
        source: Source to scrape ('yelp' or 'amazon')
        url: URL to scrape
        max_pages: Maximum number of pages to scrape
        output_path: Path to save the CSV file (optional)
    
    Returns:
        List of review dictionaries
    """
    # Create appropriate scraper
    if source.lower() == 'yelp':
        scraper = YelpScraper()
    elif source.lower() == 'amazon':
        scraper = AmazonScraper()
    else:
        raise ValueError(f"Unsupported source: {source}")
    
    # Scrape reviews
    reviews = scraper.scrape(url, max_pages)
    
    # Save to CSV if output path is provided
    if output_path and reviews:
        save_reviews_to_csv(reviews, output_path)
    
    return reviews 

def scrape_reviews_from_source(source, product_id, max_pages=3):
    """
    Scrape reviews from the specified source using the product ID
    
    Args:
        source: Source to scrape ('yelp', 'amazon', 'walmart', 'target')
        product_id: Product ID or business ID to scrape
        max_pages: Maximum number of pages to scrape
    
    Returns:
        Number of reviews scraped and saved to the database
    """
    from reviews.models import NewReview
    from ml_models.ml_utils import classify_review
    from django.conf import settings
    
    # Construct appropriate URL based on source and product ID
    if source.lower() == 'amazon':
        url = f"https://www.amazon.com/dp/{product_id}"
    elif source.lower() == 'yelp':
        url = f"https://www.yelp.com/biz/{product_id}"
    elif source.lower() == 'walmart':
        url = f"https://www.walmart.com/ip/{product_id}"
    elif source.lower() == 'target':
        url = f"https://www.target.com/p/-/A-{product_id}"
    else:
        raise ValueError(f"Unsupported source: {source}")
    
    # Scrape reviews
    scraped_reviews = scrape_reviews(source, url, max_pages)
    count = 0
    
    # Process and save reviews to database
    for review_data in scraped_reviews:
        try:
            # Extract data
            review_text = review_data.get('review_text', '')
            rating = review_data.get('rating', 0)
            
            # Skip empty reviews
            if not review_text:
                continue
            
            # Classify review
            result = classify_review(review_text)
            
            # Create review object
            review = NewReview(
                product_id=product_id,
                review_text=review_text,
                rating=rating,
                source=source.lower(),
                prediction=result['prediction_label'],
                confidence=result['confidence_score'],
                is_flagged=(result['prediction_label'] == 'fake' and 
                          result['confidence_score'] >= getattr(settings, 'CONFIDENCE_THRESHOLD', 0.7))
            )
            review.save()
            count += 1
        except Exception as e:
            logger.error(f"Error saving review: {e}")
    
    return count 