from django.db import models
import uuid

class NewReview(models.Model):
    """
    Model to store product reviews and their classification results
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_id = models.UUIDField(blank=True, null=True)  # Optional user identifier
    product_id = models.CharField(max_length=255)  # Product identifier
    product_name = models.CharField(max_length=255)
    review_text = models.TextField()  # The actual review content
    rating = models.IntegerField()
    author = models.CharField(max_length=255)
    date_posted = models.DateTimeField(auto_now_add=True)
    source = models.Char<PERSON>ield(max_length=100, default='user')
    prediction = models.CharField(max_length=10, choices=[('real', 'Real'), ('fake', 'Fake')], null=True, blank=True)
    confidence = models.FloatField(null=True, blank=True)
    is_flagged = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    
    # Metadata fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Review for {self.product_name} by {self.author}"
    
    class Meta:
        ordering = ['-date_posted']
        indexes = [
            models.Index(fields=['product_id']),
            models.Index(fields=['is_flagged']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['prediction']),
        ]
