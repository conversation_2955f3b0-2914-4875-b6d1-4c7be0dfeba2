# ReviewMate - Fake Review Detection System

A Django-based web application that uses machine learning to detect fake reviews and provide verification services.

## Quick Start

1. **Install Python 3.10+** from https://python.org
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Set up database**: `python manage.py migrate`
4. **Train ML model**: `python ml_models/train_model.py`
5. **Run server**: `python manage.py runserver`
6. **Open browser**: http://127.0.0.1:8000/

## Features

- 🤖 **AI-Powered Detection**: Machine learning model to classify fake vs real reviews
- 📊 **Confidence Scoring**: Get confidence levels for each prediction
- 💳 **Payment Verification**: Verify flagged reviews through payment system
- 📈 **Admin Dashboard**: Statistics and management interface
- 🔍 **Review Management**: Filter, search, and manage all reviews
- 🌐 **Web Scraping**: Import reviews from external sources (admin only)

## Installation

For detailed installation instructions, see [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)

## Technology Stack

- **Backend**: Django 4.2.7
- **Machine Learning**: scikit-learn, NLTK, pandas
- **Frontend**: Bootstrap, HTML/CSS/JavaScript
- **Database**: SQLite (default), supports PostgreSQL/MySQL
- **Payment**: Paystack integration

## Project Structure

```
ReviewMate/
├── ml_models/          # Machine learning components
├── reviews/            # Main review app
├── payments/           # Payment processing
├── templates/          # HTML templates
├── static/             # Static files
├── scraped_data/       # Training datasets
└── ttuguard_temp/      # Django settings
```

## Usage

1. **Submit Reviews**: Enter product reviews for analysis
2. **View Results**: See fake/real classification with confidence scores
3. **Admin Access**: Login to admin panel for advanced features
4. **Payment Verification**: Verify flagged reviews through payment

## License

This project is for educational purposes.

## Support

For installation help, see the detailed installation guide or contact the developer.
