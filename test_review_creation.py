#!/usr/bin/env python
import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ttuguard_temp.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from reviews.models import NewReview

def test_review_creation():
    """Test review creation through form submission"""
    client = Client()

    # Test form submission with fake review pattern
    data = {
        'product_id': 'TEST789',
        'product_name': 'Scam Product',
        'rating': '5',
        'author': 'Bot User',
        'review_text': 'This product changed my life completely! I bought 10 of them for my family and friends. Everyone loves it! Best purchase ever made! 100% recommend to everyone! Five stars!'
    }

    print("Testing review creation...")
    response = client.post(reverse('reviews:review_create'), data, HTTP_HOST='127.0.0.1')
    print(f'Status Code: {response.status_code}')
    
    if response.status_code == 302:
        print(f'Redirect URL: {response.get("Location", "No redirect")}')
    else:
        print("No redirect - form may have errors")
        if hasattr(response, 'context') and response.context:
            form = response.context.get('form')
            if form and form.errors:
                print(f'Form errors: {form.errors}')
    
    # Check if review was created
    latest_review = NewReview.objects.filter(product_id='TEST789').first()
    if latest_review:
        print('Review created successfully!')
        print(f'Product: {latest_review.product_name}')
        print(f'Author: {latest_review.author}')
        print(f'Prediction: {latest_review.prediction}')
        print(f'Confidence: {latest_review.confidence}')
        print(f'Flagged: {latest_review.is_flagged}')
        return True
    else:
        print('No review was created')
        return False

if __name__ == '__main__':
    test_review_creation()
