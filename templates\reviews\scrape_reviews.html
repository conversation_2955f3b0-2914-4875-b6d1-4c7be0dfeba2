{% extends 'base.html' %}

{% block title %}Scrape Reviews - ReviewMate{% endblock %}

{% block page_title %}Scrape Reviews{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Review Scraping Tool</h5>
                <small class="text-muted">Import reviews from external sources for analysis</small>
            </div>
            <div class="card-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post" class="mb-4">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="url" class="form-label">Source URL</label>
                                <input type="url" class="form-control" id="url" name="url" 
                                       placeholder="https://example.com/reviews" required>
                                <div class="form-text">Enter the URL of the page containing reviews to scrape</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_reviews" class="form-label">Max Reviews</label>
                                <input type="number" class="form-control" id="max_reviews" name="max_reviews" 
                                       value="10" min="1" max="100">
                                <div class="form-text">Maximum number of reviews to scrape</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>Start Scraping
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Supported Sources:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">✅ Amazon product reviews</li>
                            <li class="mb-2">✅ E-commerce platforms</li>
                            <li class="mb-2">✅ Review aggregator sites</li>
                            <li class="mb-2">✅ Custom review formats</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>What happens after scraping:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">🔍 Reviews are automatically analyzed by AI</li>
                            <li class="mb-2">📊 Confidence scores are calculated</li>
                            <li class="mb-2">🚩 Potentially fake reviews are flagged</li>
                            <li class="mb-2">📈 Results appear in the admin dashboard</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        {% if recent_scrapes %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">Recent Scraping Activity</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Source</th>
                                <th>Reviews Found</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for scrape in recent_scrapes %}
                            <tr>
                                <td>{{ scrape.created_at|date:"M j, Y H:i" }}</td>
                                <td>{{ scrape.source_url|truncatechars:50 }}</td>
                                <td>{{ scrape.reviews_count }}</td>
                                <td>
                                    <span class="badge bg-success">Completed</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="mt-4">
            <div class="alert alert-info">
                <h6>Important Notes:</h6>
                <ul class="mb-0">
                    <li>Ensure you have permission to scrape from the target website</li>
                    <li>Respect robots.txt and rate limiting policies</li>
                    <li>Scraped reviews will be processed through our ML model</li>
                    <li>Large scraping operations may take several minutes</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
