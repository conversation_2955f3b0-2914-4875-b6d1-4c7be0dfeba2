{% extends 'base.html' %}

{% block title %}Scrape Reviews | TTUGuard{% endblock %}

{% block page_title %}Scrape Reviews{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Scrape Reviews from External Sources</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> This tool allows you to scrape reviews from external sources like Yelp and Amazon. The scraped reviews will be automatically classified by our AI system.
                </div>
                
                <form method="post" action="{% url 'reviews:scrape_reviews' %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="source" class="form-label">Source</label>
                        <select class="form-select" id="source" name="source" required>
                            <option value="" selected disabled>Select a source</option>
                            <option value="yelp">Yelp</option>
                            <option value="amazon">Amazon</option>
                        </select>
                        <small class="form-text text-muted">Select the website to scrape reviews from.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url" name="url" placeholder="https://www.example.com/product/123" required>
                        <small class="form-text text-muted">Enter the URL of the product or business page to scrape reviews from.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max_pages" class="form-label">Max Pages</label>
                        <input type="number" class="form-control" id="max_pages" name="max_pages" min="1" max="10" value="1">
                        <small class="form-text text-muted">Maximum number of pages to scrape (1-10). Each page typically contains 10-20 reviews.</small>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> Please use this tool responsibly and respect the terms of service of the websites you are scraping from. Excessive scraping may result in your IP being blocked.
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-cloud-download"></i> Start Scraping
                        </button>
                        <a href="{% url 'reviews:admin_dashboard' %}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 