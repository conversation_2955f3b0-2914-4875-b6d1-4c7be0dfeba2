from django.db import models
import uuid

class MLModel(models.Model):
    """
    Model to store information about trained ML models
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=100)
    version = models.CharField(max_length=20)
    description = models.TextField(blank=True, null=True)
    
    # Model file path and metrics
    file_path = models.CharField(max_length=255)
    accuracy = models.FloatField(blank=True, null=True)
    precision = models.FloatField(blank=True, null=True)
    recall = models.FloatField(blank=True, null=True)
    f1_score = models.FloatField(blank=True, null=True)
    
    # Status fields
    is_active = models.BooleanField(default=False)
    
    # Metadata fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} v{self.version} ({'Active' if self.is_active else 'Inactive'})"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_active']),
        ]
