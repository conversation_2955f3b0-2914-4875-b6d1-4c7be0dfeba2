{% extends 'base.html' %}

{% block title %}Reviews - TTUGuard{% endblock %}

{% block page_title %}Reviews{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <form method="get" class="d-flex gap-2">
            <div class="input-group">
                <input type="text" name="product_id" class="form-control" placeholder="Search by product ID" value="{{ request.GET.product_id|default:'' }}">
                <button type="submit" class="btn btn-primary">Search</button>
            </div>
            <a href="{% url 'reviews:review_list' %}" class="btn btn-outline-secondary">Clear</a>
        </form>
                    </div>
    <div class="col-md-4">
        <div class="d-flex justify-content-end gap-2">
            <div class="btn-group">
                <a href="{% url 'reviews:review_list' %}" class="btn btn-outline-secondary {% if not request.GET.prediction and not request.GET.is_verified %}active{% endif %}">All</a>
                <a href="{% url 'reviews:review_list' %}?prediction=real" class="btn btn-outline-success {% if request.GET.prediction == 'real' %}active{% endif %}">Real</a>
                <a href="{% url 'reviews:review_list' %}?prediction=fake" class="btn btn-outline-danger {% if request.GET.prediction == 'fake' %}active{% endif %}">Fake</a>
                <a href="{% url 'reviews:review_list' %}?is_verified=true" class="btn btn-outline-primary {% if request.GET.is_verified == 'true' %}active{% endif %}">Verified</a>
            </div>
        </div>
    </div>
        </div>
        
{% if reviews %}
<div class="row">
            {% for review in reviews %}
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ review.product_name }}</h5>
                <div>
                        {% if review.is_verified %}
                        <span class="badge verified-badge">Verified</span>
                    {% elif review.prediction == 'fake' %}
                        <span class="badge fake-badge">Potentially Fake</span>
                    {% elif review.prediction == 'real' %}
                        <span class="badge real-badge">Real</span>
                        {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="mb-2">
                        {% for i in "12345" %}
                        {% if forloop.counter <= review.rating %}
                            <span class="text-warning">★</span>
                        {% else %}
                            <span class="text-secondary">★</span>
                        {% endif %}
                        {% endfor %}
                    <span class="ms-2">{{ review.rating }}/5</span>
                </div>
                
                <p class="card-text">{{ review.review_text|truncatechars:150 }}</p>
                
                <div class="text-muted small mb-2">
                    <div>Posted: {{ review.created_at|date:"F j, Y" }}</div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{% url 'reviews:review_detail' pk=review.id %}" class="btn btn-sm btn-primary">View Details</a>
                    
                    {% if review.prediction == 'fake' and not review.is_verified %}
                    <a href="{% url 'payments:payment_page' review_id=review.id %}" class="btn btn-sm btn-warning">Verify Review</a>
                        {% endif %}
                </div>
            </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
<nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
        {% if reviews.has_previous %}
                <li class="page-item">
            <a class="page-link" href="?page=1{% if request.GET.product_id %}&product_id={{ request.GET.product_id }}{% endif %}{% if request.GET.prediction %}&prediction={{ request.GET.prediction }}{% endif %}{% if request.GET.is_verified %}&is_verified={{ request.GET.is_verified }}{% endif %}" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
                </li>
                <li class="page-item">
            <a class="page-link" href="?page={{ reviews.previous_page_number }}{% if request.GET.product_id %}&product_id={{ request.GET.product_id }}{% endif %}{% if request.GET.prediction %}&prediction={{ request.GET.prediction }}{% endif %}{% if request.GET.is_verified %}&is_verified={{ request.GET.is_verified }}{% endif %}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
                </li>
                {% endif %}
                
        <li class="page-item disabled">
            <span class="page-link">Page {{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                </li>
                
        {% if reviews.has_next %}
                <li class="page-item">
            <a class="page-link" href="?page={{ reviews.next_page_number }}{% if request.GET.product_id %}&product_id={{ request.GET.product_id }}{% endif %}{% if request.GET.prediction %}&prediction={{ request.GET.prediction }}{% endif %}{% if request.GET.is_verified %}&is_verified={{ request.GET.is_verified }}{% endif %}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
                </li>
                <li class="page-item">
            <a class="page-link" href="?page={{ reviews.paginator.num_pages }}{% if request.GET.product_id %}&product_id={{ request.GET.product_id }}{% endif %}{% if request.GET.prediction %}&prediction={{ request.GET.prediction }}{% endif %}{% if request.GET.is_verified %}&is_verified={{ request.GET.is_verified }}{% endif %}" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        
        {% else %}
        <div class="alert alert-info">
    <p>No reviews found. {% if request.GET.product_id %}Try a different search term.{% endif %}</p>
        </div>
        {% endif %}

<div class="text-center mt-4">
    <a href="{% url 'reviews:review_create' %}" class="btn btn-success">Submit New Review</a>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %}