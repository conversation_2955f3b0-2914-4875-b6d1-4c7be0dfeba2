{% extends 'base.html' %}

{% block title %}Reviews - ReviewMate{% endblock %}

{% block page_title %}Reviews{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Product Reviews</h2>
            <a href="{% url 'reviews:review_create' %}" class="btn btn-primary">Submit Review</a>
        </div>

        {% if reviews %}
            <div class="row">
                {% for review in reviews %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0 text-truncate">{{ review.product_name }}</h6>
                            <div>
                                {% if review.is_verified %}
                                    <span class="badge bg-success">Verified</span>
                                {% elif review.prediction == 'fake' %}
                                    <span class="badge bg-danger">Potentially Fake</span>
                                {% elif review.prediction == 'real' %}
                                    <span class="badge bg-success">Real</span>
                                {% else %}
                                    <span class="badge bg-secondary">Analyzing...</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <div class="d-flex align-items-center">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}
                                            <span class="text-warning">★</span>
                                        {% else %}
                                            <span class="text-secondary">★</span>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="ms-2 small text-muted">{{ review.rating }}/5</span>
                                </div>
                            </div>
                            
                            <p class="card-text">{{ review.review_text|truncatewords:20 }}</p>
                            
                            {% if review.prediction %}
                            <div class="small text-muted mb-2">
                                <strong>AI Analysis:</strong>
                                {% if review.is_verified %}
                                    Verified as genuine
                                {% elif review.prediction == 'fake' %}
                                    Flagged as potentially fake
                                    {% if review.confidence %}
                                        ({% widthratio review.confidence 1 100 %}% confidence)
                                    {% endif %}
                                {% else %}
                                    Appears genuine
                                    {% if review.confidence %}
                                        ({% widthratio review.confidence 1 100 %}% confidence)
                                    {% endif %}
                                {% endif %}
                            </div>
                            {% endif %}
                            
                            <div class="small text-muted">
                                {{ review.created_at|date:"M j, Y" }}
                                <span class="ms-2">{{ review.product_id }}</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{% url 'reviews:review_detail' pk=review.id %}" class="btn btn-outline-primary btn-sm">View Details</a>
                                {% if review.prediction == 'fake' and not review.is_verified %}
                                    <a href="{% url 'payments:payment_page' review_id=review.id %}" class="btn btn-warning btn-sm">Verify</a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <h4 class="text-muted">No Reviews Yet</h4>
                <p class="text-muted mb-4">Be the first to submit a review for analysis!</p>
                <a href="{% url 'reviews:review_create' %}" class="btn btn-primary">Submit First Review</a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
