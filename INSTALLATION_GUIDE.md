# TTUGuard - Complete Installation Guide for Windows 10

This guide provides step-by-step instructions to set up and run the TTUGuard Fake Review Detection System on a fresh Windows 10 PC.

## Prerequisites

Before starting, ensure you have:
- Windows 10 PC with internet connection
- Administrator privileges on the computer
- At least 2GB of free disk space

## Step 1: Install Python 3.10+

1. **Download Python:**
   - Go to https://www.python.org/downloads/
   - Download Python 3.10 or higher (recommended: Python 3.10.11)

2. **Install Python:**
   - Run the downloaded installer
   - ⚠️ **IMPORTANT**: Check "Add Python to PATH" during installation
   - Choose "Install Now"
   - Verify installation by opening Command Prompt and typing: `python --version`

## Step 2: Download the TTUGuard Project

1. **Get the project files:**
   - Download the TTUGuard project folder
   - Extract to a location like `C:\TTUGuard\` or your Desktop

2. **Open Command Prompt in project directory:**
   - Navigate to the TTUGuard folder
   - Hold Shift + Right-click in the folder
   - Select "Open PowerShell window here" or "Open command window here"

## Step 3: Install Required Dependencies

1. **Upgrade pip (recommended):**
   ```bash
   python -m pip install --upgrade pip
   ```

2. **Install project dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

   This will install:
   - Django (web framework)
   - scikit-learn (machine learning)
   - pandas, numpy (data processing)
   - nltk (natural language processing)
   - beautifulsoup4 (web scraping)
   - And other required packages

3. **Download NLTK data:**
   ```bash
   python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
   ```

## Step 4: Set Up the Database

1. **Apply database migrations:**
   ```bash
   python manage.py migrate
   ```

2. **Create an admin user (optional but recommended):**
   ```bash
   python manage.py createsuperuser
   ```
   - Enter a username (e.g., admin)
   - Enter an email address
   - Enter a password (remember this!)

## Step 5: Train the Machine Learning Model

**Train the model using the included dataset:**
```bash
python ml_models/train_model.py
```

This will:
- Use the kaggle_dataset.csv file for training
- Create a trained model for fake review detection
- Save the model to the database

## Step 6: Run the Application

1. **Start the development server:**
   ```bash
   python manage.py runserver
   ```

2. **Access the application:**
   - Open your web browser
   - Go to: http://127.0.0.1:8000/
   - You should see the TTUGuard interface

3. **Access admin panel (if you created a superuser):**
   - Go to: http://127.0.0.1:8000/admin/
   - Login with your superuser credentials

## Step 7: Test the Application

1. **Submit a test review:**
   - Click "Submit New Review"
   - Enter a product ID (e.g., "TEST123")
   - Enter review text (try both genuine and suspicious reviews)
   - Submit and see the ML prediction

2. **Example test reviews:**
   - **Real review**: "This product works well and arrived on time. Good quality for the price."
   - **Fake review**: "AMAZING PRODUCT!!!! Best purchase ever! Changed my life completely! 5 stars!"

## Troubleshooting

### Common Issues and Solutions

**1. "Python is not recognized" error:**
- Reinstall Python and ensure "Add Python to PATH" is checked
- Restart Command Prompt after installation

**2. "pip is not recognized" error:**
- Try using: `python -m pip` instead of just `pip`

**3. Package installation fails:**
- Try: `pip install --user -r requirements.txt`
- Or install packages one by one if needed

**4. NLTK download errors:**
- Run Python interactively: `python`
- Then execute:
  ```python
  import nltk
  nltk.download('punkt')
  nltk.download('stopwords')
  exit()
  ```

**5. "NotFittedError" when submitting reviews:**
- Make sure you ran: `python ml_models/train_model.py`
- Check that the model was trained successfully

**6. Database errors:**
- Delete `db.sqlite3` file and run migrations again:
  ```bash
  python manage.py migrate
  ```

**7. Port already in use:**
- Use a different port: `python manage.py runserver 8001`

## Features Overview

Once running, TTUGuard provides:

1. **Review Submission**: Submit reviews for fake detection analysis
2. **ML Classification**: Automatic fake/real classification with confidence scores
3. **Admin Dashboard**: View statistics and manage reviews (requires superuser)
4. **Payment Verification**: Flagged reviews can be verified through payment
5. **Review Management**: View, filter, and manage all submitted reviews

## Project Structure

```
TTUGuard/
├── manage.py                 # Django management script
├── requirements.txt          # Python dependencies
├── db.sqlite3               # Database (created after migrations)
├── ml_models/               # Machine learning components
│   ├── train_model.py       # Model training script
│   ├── ml_utils.py          # ML utilities
│   └── trained_models/      # Saved ML models
├── reviews/                 # Main app for reviews
│   ├── views.py             # Web views
│   ├── models.py            # Database models
│   └── forms.py             # Web forms
├── payments/                # Payment processing
├── templates/               # HTML templates
├── static/                  # CSS, JS, images
├── scraped_data/            # Training data
│   └── kaggle_dataset.csv   # ML training dataset
└── ttuguard_temp/           # Django project settings
    ├── settings.py          # Configuration
    └── urls.py              # URL routing
```

## Next Steps

After successful installation:

1. **Explore the interface** - Submit different types of reviews
2. **Check the admin panel** - View statistics and manage data
3. **Customize settings** - Modify confidence thresholds in settings.py
4. **Add more data** - Use the admin scraping feature to gather more reviews

## Support

If you encounter issues:
1. Check this troubleshooting section first
2. Ensure all steps were followed correctly
3. Contact the developer for assistance

---

**Congratulations!** You now have TTUGuard running on your Windows 10 PC.
