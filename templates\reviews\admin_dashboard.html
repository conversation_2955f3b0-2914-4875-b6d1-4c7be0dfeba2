{% extends 'base.html' %}

{% block title %}Admin Dashboard - ReviewMate{% endblock %}

{% block page_title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">{{ total_reviews }}</h5>
                        <p class="card-text">Total Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">{{ fake_reviews }}</h5>
                        <p class="card-text">Fake Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">{{ verified_reviews }}</h5>
                        <p class="card-text">Verified Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">{{ flagged_reviews }}</h5>
                        <p class="card-text">Flagged Reviews</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Reviews</h5>
                <div>
                    <a href="{% url 'reviews:review_create' %}" class="btn btn-primary btn-sm">
                        Add Review
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_reviews %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Rating</th>
                                <th>Review Text</th>
                                <th>Prediction</th>
                                <th>Confidence</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for review in recent_reviews %}
                            <tr>
                                <td>{{ review.product_name|truncatechars:30 }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ review.rating }}</span>
                                        <div class="text-warning">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="bi bi-star-fill"></i>
                                                {% else %}
                                                    <i class="bi bi-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </td>
                                <td>{{ review.review_text|truncatechars:50 }}</td>
                                <td>
                                    {% if review.prediction == 'fake' %}
                                        <span class="badge bg-danger">Fake</span>
                                    {% elif review.prediction == 'real' %}
                                        <span class="badge bg-success">Real</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Unknown</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if review.confidence %}
                                        {% widthratio review.confidence 1 100 %}%
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if review.is_verified %}
                                        <span class="badge bg-success">Verified</span>
                                    {% elif review.is_flagged %}
                                        <span class="badge bg-warning">Flagged</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Normal</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'reviews:review_detail' pk=review.id %}" 
                                           class="btn btn-outline-primary btn-sm">View</a>
                                        {% if review.prediction == 'fake' and not review.is_verified %}
                                            <a href="{% url 'payments:payment_page' review_id=review.id %}" 
                                               class="btn btn-outline-warning btn-sm">Verify</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <p class="text-muted mt-3">No reviews found</p>
                    <a href="{% url 'reviews:review_create' %}" class="btn btn-primary">Create First Review</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
