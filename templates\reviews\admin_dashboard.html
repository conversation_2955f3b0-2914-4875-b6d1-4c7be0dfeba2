{% extends 'base.html' %}

{% block title %}Admin Dashboard | TTUGuard{% endblock %}

{% block page_title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <h5 class="card-title">Total Reviews</h5>
                <p class="card-text display-4">{{ total_reviews }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <h5 class="card-title">Fake Reviews</h5>
                <p class="card-text display-4">{{ fake_reviews }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <h5 class="card-title">Flagged Reviews</h5>
                <p class="card-text display-4">{{ flagged_reviews }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <h5 class="card-title">Verified Reviews</h5>
                <p class="card-text display-4">{{ verified_reviews }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Reviews</h5>
                <div>
                    <a href="{% url 'reviews:review_list' %}" class="btn btn-sm btn-primary">View All</a>
                    <a href="{% url 'reviews:scrape_reviews' %}" class="btn btn-sm btn-success">Scrape Reviews</a>
                </div>
            </div>
            <div class="card-body">
                {% if recent_reviews %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Product ID</th>
                                <th>Rating</th>
                                <th>Review</th>
                                <th>Prediction</th>
                                <th>Confidence</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for review in recent_reviews %}
                            <tr>
                                <td>{{ review.product_id }}</td>
                                <td>{{ review.rating }}/5</td>
                                <td>{{ review.review_text|truncatechars:50 }}</td>
                                <td>
                                    <span class="badge {% if review.prediction == 'fake' %}fake-badge{% else %}real-badge{% endif %}">
                                        {{ review.prediction|title }}
                                    </span>
                                </td>
                                <td>{{ review.confidence|floatformat:2 }}</td>
                                <td>{{ review.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <a href="{% url 'reviews:review_detail' review.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="{% url 'reviews:override_review' review.id %}" class="btn btn-sm btn-outline-warning">Override</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No reviews found.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="{% url 'reviews:scrape_reviews' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-cloud-download"></i> Scrape Reviews from External Sources
                    </a>
                    <a href="{% url 'reviews:review_list' %}?is_flagged=true" class="list-group-item list-group-item-action">
                        <i class="bi bi-flag"></i> View Flagged Reviews
                    </a>
                    <a href="{% url 'reviews:review_list' %}?prediction=fake" class="list-group-item list-group-item-action">
                        <i class="bi bi-exclamation-triangle"></i> View Fake Reviews
                    </a>
                    <a href="{% url 'admin:reviews_newreview_changelist' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-gear"></i> Manage Reviews in Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">System Status</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        ML Model Status
                        <span class="badge bg-success">Active</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Confidence Threshold
                        <span>{{ confidence_threshold|default:"0.7" }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Payment Integration
                        <span class="badge bg-success">Active</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Verification Fee
                        <span>{{ verification_amount|default:"5.00" }} GHS</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 