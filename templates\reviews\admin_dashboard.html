{% extends 'base.html' %} {% load review_extras %} {% block title %}Admin
Dashboard | ReviewMate{% endblock %} {% block page_title %}Admin Dashboard{%
endblock %} {% block content %}
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body">
        <h5 class="card-title">Total Reviews</h5>
        <p class="card-text display-4">{{ total_reviews }}</p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-danger">
      <div class="card-body">
        <h5 class="card-title">Fake Reviews</h5>
        <p class="card-text display-4">{{ fake_reviews }}</p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body">
        <h5 class="card-title">Flagged Reviews</h5>
        <p class="card-text display-4">{{ flagged_reviews }}</p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body">
        <h5 class="card-title">Verified Reviews</h5>
        <p class="card-text display-4">{{ verified_reviews }}</p>
      </div>
    </div>
  </div>
</div>

<div class="row mb-4">
  <div class="col-md-12">
    <div class="card">
      <div
        class="card-header d-flex justify-content-between align-items-center"
      >
        <h5 class="mb-0">Recent Reviews</h5>
        <div>
          <a
            href="{% url 'reviews:review_list' %}"
            class="btn btn-sm btn-primary"
            >View All</a
          >
          <a
            href="{% url 'reviews:scrape_reviews' %}"
            class="btn btn-sm btn-secondary"
            >Scrape Reviews</a
          >
        </div>
      </div>
      <div class="card-body">
        {% if recent_reviews %}
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Product</th>
                <th>Rating</th>
                <th>Review</th>
                <th>Prediction</th>
                <th>Confidence</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for review in recent_reviews %}
              <tr>
                <td>{{ review.product_name|truncatechars:30 }}</td>
                <td>
                  <div class="d-flex align-items-center">
                    <span class="me-2">{{ review.rating }}</span>
                    <div class="text-warning">
                      {% for i in "12345" %} {% if forloop.counter <=
                      review.rating %}
                      <i class="bi bi-star-fill"></i>
                      {% else %}
                      <i class="bi bi-star"></i>
                      {% endif %} {% endfor %}
                    </div>
                  </div>
                </td>
                <td>{{ review.review_text|truncatechars:50 }}</td>
                <td>
                  {% if review.prediction == 'fake' %}
                  <span class="badge bg-danger">Fake</span>
                  {% elif review.prediction == 'real' %}
                  <span class="badge bg-success">Real</span>
                  {% else %}
                  <span class="badge bg-secondary">Unknown</span>
                  {% endif %}
                </td>
                <td>
                  {% if review.confidence %} {{ review.confidence|percentage }}%
                  {% else %} N/A {% endif %}
                </td>
                <td>
                  {% if review.is_verified %}
                  <span class="badge bg-success">Verified</span>
                  {% elif review.is_flagged %}
                  <span class="badge bg-warning">Flagged</span>
                  {% else %}
                  <span class="badge bg-secondary">Normal</span>
                  {% endif %}
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a
                      href="{% url 'reviews:review_detail' pk=review.id %}"
                      class="btn btn-sm btn-outline-primary"
                      >View</a
                    >
                    {% if review.is_flagged and not review.is_verified %}
                    <a
                      href="{% url 'payments:payment_page' review_id=review.id %}"
                      class="btn btn-sm btn-outline-warning"
                      >Verify</a
                    >
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="bi bi-inbox display-1 text-muted"></i>
          <p class="text-muted mt-3">No reviews found</p>
          <a href="{% url 'reviews:review_create' %}" class="btn btn-primary"
            >Create First Review</a
          >
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">ML Model Performance</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <div class="text-center">
              <h6 class="text-muted">Accuracy</h6>
              <p class="display-6 text-success">
                {{ model_accuracy|default:"N/A" }}
              </p>
            </div>
          </div>
          <div class="col-6">
            <div class="text-center">
              <h6 class="text-muted">Total Predictions</h6>
              <p class="display-6 text-info">
                {{ total_predictions|default:"0" }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{% url 'reviews:review_create' %}" class="btn btn-primary"
            >Add New Review</a
          >
          <a href="{% url 'reviews:scrape_reviews' %}" class="btn btn-secondary"
            >Scrape Reviews</a
          >
          <a href="{% url 'admin:index' %}" class="btn btn-outline-primary"
            >Django Admin</a
          >
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
{% endblock %}
