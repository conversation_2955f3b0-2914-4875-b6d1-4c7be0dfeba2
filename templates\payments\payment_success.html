{% extends 'base.html' %}

{% block title %}Payment Successful - ReviewMate{% endblock %}

{% block page_title %}Payment Successful{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-8 offset-md-2">
    <div class="card">
      <div class="card-header bg-success text-white">
        <h5 class="mb-0">Payment Successful</h5>
      </div>
      <div class="card-body">
        <div class="text-center mb-4">
          <i
            class="bi bi-check-circle-fill text-success"
            style="font-size: 4rem"
          ></i>
        </div>

        <div class="alert alert-success">
          <p>
            Your payment has been successfully processed and the review has been
            verified.
          </p>
          <p>Reference: <strong>{{ payment.reference }}</strong></p>
        </div>

        <div class="mb-4">
          <h6>Review Details</h6>
          <div class="card bg-light p-3">
            <p><strong>Product:</strong> {{ review.product_name }}</p>
            <p><strong>Rating:</strong> {{ review.rating }} / 5</p>
            <p>
              <strong>Status:</strong>
              <span class="badge bg-primary">Verified</span>
            </p>
          </div>
        </div>

        <div class="d-grid gap-2">
          <a
            href="{% url 'reviews:review_detail' pk=review.id %}"
            class="btn btn-primary"
          >
            View Review
          </a>
          <a
            href="{% url 'reviews:review_list' %}"
            class="btn btn-outline-secondary"
          >
            Back to Reviews
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
{% endblock %}
