{% extends 'base.html' %}

{% block title %}Verify Review - ReviewMate{% endblock %}

{% block page_title %}Verify Review{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-8 offset-md-2">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">Verify Review</h5>
      </div>
      <div class="card-body">
        <div class="alert alert-info">
          <p>
            Our AI system has flagged this review as potentially fake. To verify
            this review, please complete the payment below.
          </p>
        </div>

        <div class="mb-4">
          <h6>Review Details</h6>
          <div class="card bg-light p-3">
            <p><strong>Product:</strong> {{ review.product_name }}</p>
            <p><strong>Rating:</strong> {{ review.rating }} / 5</p>
            <p><strong>Content:</strong> {{ review.review_text }}</p>
          </div>
        </div>

        <div class="mb-4">
          <h6>Payment Information</h6>
          <div class="card bg-light p-3">
            <p><strong>Amount:</strong> GHS {{ payment.amount }}</p>
            <p><strong>Reference:</strong> {{ reference }}</p>
          </div>
        </div>

        <form id="paymentForm">
          <div class="mb-3">
            <label for="email" class="form-label">Email Address</label>
            <input type="email" id="email" class="form-control" required />
          </div>

          <div class="d-grid gap-2">
            <button
              type="submit"
              class="btn btn-primary"
              onclick="payWithPaystack(event)"
            >
              Pay Now
            </button>
            <a
              href="{% url 'reviews:review_detail' pk=review.id %}"
              class="btn btn-outline-secondary"
              >Cancel</a
            >
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://js.paystack.co/v1/inline.js"></script>
<script>
  function payWithPaystack(e) {
    e.preventDefault();

    let handler = PaystackPop.setup({
      key: "{{ paystack_public_key }}",
      email: document.getElementById("email").value,
      amount: parseInt("{{ amount_kobo }}"),
      currency: "GHS",
      ref: "{{ reference }}",
      callback: function (response) {
        window.location.href =
          "{% url 'payments:verify_payment' reference=reference %}";
      },
      onClose: function () {
        alert("Transaction was not completed, window closed.");
      },
    });
    handler.openIframe();
  }
</script>
{% endblock %}

{% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
{% endblock %}
