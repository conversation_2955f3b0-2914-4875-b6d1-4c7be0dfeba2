import os
import pickle
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from django.conf import settings

# Download required NLTK data
nltk.download('punkt', quiet=True)
nltk.download('stopwords', quiet=True)

class FakeReviewDetector:
    """
    Class for training and using the fake review detection model
    """
    def __init__(self, model_path=None):
        """
        Initialize the detector with an optional pre-trained model
        """
        self.model = None
        self.vectorizer = None
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            # Initialize a new model
            self.vectorizer = TfidfVectorizer(
                max_features=10000, 
                stop_words='english',
                ngram_range=(1, 3)
            )
            self.model = LogisticRegression(C=1.0, class_weight='balanced')
    
    def preprocess_text(self, text):
        """
        Preprocess text by tokenizing, removing stopwords, and lowercasing
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and non-alphabetic tokens
        stop_words = set(stopwords.words('english'))
        tokens = [word for word in tokens if word.isalpha() and word not in stop_words]
        
        # Join tokens back into a string
        return " ".join(tokens)
    
    def train(self, data_path, text_column='text', label_column='fake'):
        """
        Train the model using data from the specified path
        
        Args:
            data_path: Path to the CSV file with training data
            text_column: Name of the column containing review text
            label_column: Name of the column containing labels (1 for fake, 0 for real)
        
        Returns:
            Dictionary with model performance metrics
        """
        # Load data
        df = pd.read_csv(data_path)
        
        # Preprocess text
        df['processed_text'] = df[text_column].apply(self.preprocess_text)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            df['processed_text'], 
            df[label_column], 
            test_size=0.2, 
            random_state=42
        )
        
        # Create pipeline
        pipeline = Pipeline([
            ('tfidf', self.vectorizer),
            ('classifier', self.model)
        ])
        
        # Train model
        pipeline.fit(X_train, y_train)
        
        # Evaluate model
        y_pred = pipeline.predict(X_test)
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred),
            'recall': recall_score(y_test, y_pred),
            'f1_score': f1_score(y_test, y_pred)
        }
        
        # Save pipeline as model
        self.model = pipeline
        
        return metrics
    
    def predict(self, text):
        """
        Predict whether a review is fake or real
        
        Args:
            text: Review text
        
        Returns:
            Dictionary with prediction label and confidence score
        """
        if not self.model:
            raise ValueError("Model not trained or loaded")
        
        # Preprocess text
        processed_text = self.preprocess_text(text)
        
        # Get prediction probabilities
        probs = self.model.predict_proba([processed_text])[0]
        
        # Get predicted class (0 = real, 1 = fake)
        pred_class = self.model.predict([processed_text])[0]
        
        # Get confidence score (probability of predicted class)
        confidence = probs[pred_class]
        
        # Return prediction
        return {
            'prediction_label': 'fake' if pred_class == 1 else 'real',
            'confidence_score': float(confidence)
        }
    
    def save_model(self, path):
        """
        Save the trained model to disk
        """
        if not self.model:
            raise ValueError("No trained model to save")
        
        with open(path, 'wb') as f:
            pickle.dump(self.model, f)
    
    def load_model(self, path):
        """
        Load a trained model from disk
        """
        with open(path, 'rb') as f:
            self.model = pickle.load(f)

# Helper functions for the Django app
def get_active_model():
    """
    Get the currently active ML model
    """
    from ml_models.models import MLModel
    
    try:
        model_info = MLModel.objects.filter(is_active=True).latest('created_at')
        detector = FakeReviewDetector(model_info.file_path)
        return detector
    except:
        # Return a default model if no active model exists
        default_model_path = getattr(settings, 'ML_MODEL_PATH', None)
        if default_model_path and os.path.exists(default_model_path):
            return FakeReviewDetector(default_model_path)
        return FakeReviewDetector()

def classify_review(review_text):
    """
    Classify a review using the active model
    """
    detector = get_active_model()
    return detector.predict(review_text)

def predict_review(review_text):
    """
    Alias for classify_review for backward compatibility
    """
    return classify_review(review_text) 