from django.contrib import admin
from .models import NewReview

@admin.register(NewReview)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_id', 'rating', 'prediction', 'confidence', 'is_flagged', 'is_verified', 'created_at')
    list_filter = ('is_flagged', 'is_verified', 'prediction', 'rating', 'source')
    search_fields = ('product_id', 'review_text')
    readonly_fields = ('id', 'created_at', 'updated_at')
    fieldsets = (
        ('Review Information', {
            'fields': ('id', 'user_id', 'product_id', 'product_name', 'review_text', 'rating', 'author', 'source')
        }),
        ('Classification', {
            'fields': ('prediction', 'confidence', 'is_flagged', 'is_verified')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )
