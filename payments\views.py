from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
import json
import requests
import uuid
from django.urls import reverse
from django.utils import timezone

from .models import Payment
from reviews.models import NewReview

def payment_page(request, review_id):
    """Display payment page for a review that needs verification"""
    review = get_object_or_404(NewReview, id=review_id)
    
    # Check if review is already verified
    if review.is_verified:
        messages.info(request, "This review is already verified.")
        return redirect('reviews:review_detail', pk=review_id)
    
    # Get amount from settings
    amount = float(settings.VERIFICATION_AMOUNT)
    
    # Generate a unique reference
    reference = str(uuid.uuid4())
    
    # Create payment record
    payment = Payment.objects.create(
        review=review,
        amount=amount,
        reference=reference,
        email=request.POST.get('email', '<EMAIL>')
    )
    
    context = {
        'review': review,
        'payment': payment,
        'paystack_public_key': settings.PAYSTACK_KEY_ID,
        'amount_kobo': int(amount * 100),  # Paystack uses kobo (100 kobo = 1 GHS)
        'email': payment.email,
        'reference': reference,
    }
    
    return render(request, 'payments/payment_page.html', context)

@csrf_exempt
def verify_payment(request, reference):
    """Verify payment with Paystack API"""
    # Get the payment
    payment = get_object_or_404(Payment, reference=reference)
    review = payment.review
    
    # Call Paystack API to verify payment
    url = f"https://api.paystack.co/transaction/verify/{reference}"
    headers = {
        "Authorization": f"Bearer {settings.PAYSTACK_KEY_SECRET}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        response_data = response.json()
        
        if response_data['data']['status'] == 'success':
            # Update payment status
            payment.verified = True
            payment.save()
            
            # Verify the review
            review.is_verified = True
            review.save()
            
            # Redirect to success page
            return redirect('payments:payment_success', reference=reference)
    
    # If anything fails, redirect to failure page
    return redirect('payments:payment_failure', reference=reference)

def payment_success(request, reference):
    """Display payment success page"""
    payment = get_object_or_404(Payment, reference=reference)
    review = payment.review
    
    context = {
        'payment': payment,
        'review': review,
    }
    
    return render(request, 'payments/payment_success.html', context)

def payment_failure(request, reference):
    """Display payment failure page"""
    payment = get_object_or_404(Payment, reference=reference)
    review = payment.review
    
    context = {
        'payment': payment,
        'review': review,
    }
    
    return render(request, 'payments/payment_failure.html', context)

@csrf_exempt
def paystack_webhook(request):
    """Handle Paystack webhook events"""
    if request.method != 'POST':
        return HttpResponse(status=400)
    
    # Get the payload
    payload = json.loads(request.body)
    event = payload.get('event')
    
    # Handle successful charge
    if event == 'charge.success':
        reference = payload['data']['reference']
        try:
            payment = Payment.objects.get(reference=reference)
            payment.verified = True
            payment.save()
            
            # Verify the review
            review = payment.review
            review.is_verified = True
            review.save()
        except Payment.DoesNotExist:
            pass
    
    return HttpResponse(status=200)
