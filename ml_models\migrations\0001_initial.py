# Generated by Django 4.2.7 on 2025-06-25 20:44

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MLModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('version', models.CharField(max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('file_path', models.CharField(max_length=255)),
                ('accuracy', models.FloatField(blank=True, null=True)),
                ('precision', models.FloatField(blank=True, null=True)),
                ('recall', models.FloatField(blank=True, null=True)),
                ('f1_score', models.FloatField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['is_active'], name='ml_models_m_is_acti_afbf29_idx')],
            },
        ),
    ]
