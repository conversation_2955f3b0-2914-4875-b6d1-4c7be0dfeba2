from django import forms
from .models import NewReview

class ReviewForm(forms.ModelForm):
    """
    Form for submitting a review
    """
    RATING_CHOICES = [(i, str(i)) for i in range(1, 6)]
    
    rating = forms.ChoiceField(
        choices=RATING_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='Rating'
    )
    
    class Meta:
        model = NewReview
        fields = ['product_id', 'product_name', 'review_text', 'rating', 'author']
        widgets = {
            'review_text': forms.Textarea(attrs={'rows': 5, 'placeholder': 'Write your review here...', 'class': 'form-control'}),
            'product_id': forms.TextInput(attrs={'placeholder': 'Product ID', 'class': 'form-control'}),
            'product_name': forms.TextInput(attrs={'placeholder': 'Product Name', 'class': 'form-control'}),
            'author': forms.TextInput(attrs={'placeholder': 'Your Name', 'class': 'form-control'}),
        }
        labels = {
            'product_id': 'Product ID',
            'product_name': 'Product Name',
            'review_text': 'Review',
            'author': 'Your Name',
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make all fields required
        for field in self.fields:
            self.fields[field].required = True


class ReviewFilterForm(forms.Form):
    """
    Form for filtering reviews
    """
    product_id = forms.CharField(required=False, label='Product ID')
    is_flagged = forms.ChoiceField(
        required=False,
        label='Flagged',
        choices=[('', 'All'), ('true', 'Yes'), ('false', 'No')]
    )
    is_verified = forms.ChoiceField(
        required=False,
        label='Verified',
        choices=[('', 'All'), ('true', 'Yes'), ('false', 'No')]
    )
    prediction = forms.ChoiceField(
        required=False,
        label='Prediction',
        choices=[('', 'All'), ('fake', 'Fake'), ('real', 'Real')]
    )
    min_confidence = forms.FloatField(
        required=False,
        label='Min Confidence',
        min_value=0.0,
        max_value=1.0,
        widget=forms.NumberInput(attrs={'step': '0.1'})
    )
    max_confidence = forms.FloatField(
        required=False,
        label='Max Confidence',
        min_value=0.0,
        max_value=1.0,
        widget=forms.NumberInput(attrs={'step': '0.1'})
    )


class ScrapeReviewsForm(forms.Form):
    """
    Form for scraping reviews from external sources
    """
    SOURCE_CHOICES = [
        ('yelp', 'Yelp'),
        ('amazon', 'Amazon'),
    ]
    
    source = forms.ChoiceField(
        choices=SOURCE_CHOICES,
        label='Source',
        required=True
    )
    url = forms.URLField(
        label='URL',
        required=True,
        widget=forms.URLInput(attrs={'placeholder': 'https://www.example.com/product/123'})
    )
    max_pages = forms.IntegerField(
        label='Max Pages',
        required=False,
        initial=1,
        min_value=1,
        max_value=10,
        help_text='Maximum number of pages to scrape (1-10)'
    ) 