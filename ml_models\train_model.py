import os
import sys
import django
import argparse
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ttuguard_temp.settings')
django.setup()

from ml_models.ml_utils import FakeReviewDetector
from ml_models.models import MLModel
from django.conf import settings

def train_model(data_path=None, model_name=None, activate=True):
    """
    Train a new fake review detection model and save it to the database
    
    Args:
        data_path: Path to the training data CSV file (default: kaggle_dataset.csv)
        model_name: Name for the new model (default: 'fake_review_detector')
        activate: Whether to set this model as active (default: True)
    
    Returns:
        MLModel instance
    """
    # Always use kaggle_dataset.csv
    data_path = os.path.join(settings.BASE_DIR, 'scraped_data', 'kaggle_dataset.csv')
    
    # Create model name and version if not provided
    if not model_name:
        model_name = 'fake_review_detector'
    
    version = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Create directory for model if it doesn't exist
    model_dir = os.path.join(settings.BASE_DIR, 'ml_models', 'trained_models')
    os.makedirs(model_dir, exist_ok=True)
    
    # Define model file path
    model_file = os.path.join(model_dir, f"{model_name}_{version}.pkl")
    
    # Train model
    detector = FakeReviewDetector()
    metrics = detector.train(data_path)
    
    # Save model to file
    detector.save_model(model_file)
    
    # Create model record in database
    model_record = MLModel.objects.create(
        name=model_name,
        version=version,
        description=f"Trained on kaggle_dataset.csv",
        file_path=model_file,
        accuracy=metrics.get('accuracy'),
        precision=metrics.get('precision'),
        recall=metrics.get('recall'),
        f1_score=metrics.get('f1_score'),
        is_active=activate
    )
    
    # If activating this model, deactivate all others
    if activate:
        MLModel.objects.exclude(id=model_record.id).update(is_active=False)
    
    return model_record

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train a fake review detection model')
    parser.add_argument('--model-name', type=str, help='Name for the new model')
    parser.add_argument('--no-activate', action='store_true', help='Do not set this model as active')
    
    args = parser.parse_args()
    
    model = train_model(
        model_name=args.model_name,
        activate=not args.no_activate
    )
    
    print(f"Model trained successfully: {model.name} (version {model.version})")
    print(f"Model file: {model.file_path}")
    print(f"Metrics: accuracy={model.accuracy:.4f}, precision={model.precision:.4f}, recall={model.recall:.4f}, f1={model.f1_score:.4f}")
    
    if model.is_active:
        print("This model is now set as the active model.")
    else:
        print("This model is not set as the active model.") 