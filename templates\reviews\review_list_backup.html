{% extends 'base.html' %} {% block title %}All Reviews - ReviewMate{% endblock
%} {% block page_title %}All Reviews{% endblock %} {% block content %}
<div class="row mb-4">
  <div class="col-md-8">
    <h2>All Reviews</h2>
    <p class="text-muted">Browse all submitted reviews and their AI analysis</p>
  </div>
  <div class="col-md-4 text-end">
    <a href="{% url 'reviews:review_create' %}" class="btn btn-primary">
      <i class="bi bi-plus-circle"></i> Add New Review
    </a>
  </div>
</div>

<!-- Filter and Stats -->
<div class="row mb-4">
  <div class="col-md-12">
    <div class="card">
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-3">
            <h5 class="text-primary">{{ total_reviews }}</h5>
            <small class="text-muted">Total Reviews</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-success">{{ real_reviews }}</h5>
            <small class="text-muted">Real Reviews</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-danger">{{ fake_reviews }}</h5>
            <small class="text-muted">Potentially Fake</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-info">{{ verified_reviews }}</h5>
            <small class="text-muted">Verified Reviews</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Reviews List -->
{% if object_list %} {% for review in object_list %}
<div class="card mb-3">
  <div class="card-header d-flex justify-content-between align-items-center">
    <div>
      <h6 class="mb-0">{{ review.product_name }}</h6>
      <small class="text-muted"
        >by {{ review.author }} • {{ review.created_at|date:"M j, Y" }}</small
      >
    </div>
    <div>
      {% if review.is_verified %}
      <span class="badge verified-badge">✓ Verified</span>
      {% elif review.prediction == 'fake' %}
      <span class="badge fake-badge">⚠ Potentially Fake</span>
      {% elif review.prediction == 'real' %}
      <span class="badge real-badge">✓ Real</span>
      {% else %}
      <span class="badge bg-secondary">Pending Analysis</span>
      {% endif %}
    </div>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-8">
        <!-- Rating -->
        <div class="mb-2">
          {% for i in "12345" %} {% if forloop.counter <= review.rating %}
          <span class="text-warning">★</span>
          {% else %}
          <span class="text-secondary">★</span>
          {% endif %} {% endfor %}
          <span class="ms-2">{{ review.rating }}/5</span>
        </div>

        <!-- Review Text (truncated) -->
        <p class="mb-2">
          {% if review.review_text|length > 150 %} {{
          review.review_text|truncatewords:25 }} {% else %} {{
          review.review_text }} {% endif %}
        </p>
      </div>
      <div class="col-md-4">
        {% if review.prediction %}
        <div class="small">
          <strong>AI Analysis:</strong><br />
          {% if review.is_verified %}
          <span class="text-success">Verified as Real</span>
          {% elif review.prediction == 'fake' %}
          <span class="text-danger">Likely Fake</span>
          {% if review.confidence %}
          <br /><small
            >{% widthratio review.confidence 1 100 %}% confidence</small
          >
          {% endif %} {% else %}
          <span class="text-success">Appears Genuine</span>
          {% if review.confidence %}
          <br /><small
            >{% widthratio review.confidence 1 100 %}% confidence</small
          >
          {% endif %} {% endif %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>
  <div class="card-footer">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <a
          href="{% url 'reviews:review_detail' review.id %}"
          class="btn btn-sm btn-outline-primary"
        >
          View Details
        </a>
        {% if review.prediction == 'fake' and not review.is_verified %}
        <a
          href="{% url 'payments:payment_page' review_id=review.id %}"
          class="btn btn-sm btn-warning"
        >
          Verify Review
        </a>
        {% endif %}
      </div>
      <small class="text-muted">Product ID: {{ review.product_id }}</small>
    </div>
  </div>
</div>
{% endfor %}

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="Reviews pagination">
  <ul class="pagination justify-content-center">
    {% if page_obj.has_previous %}
    <li class="page-item">
      <a class="page-link" href="?page=1">First</a>
    </li>
    <li class="page-item">
      <a class="page-link" href="?page={{ page_obj.previous_page_number }}"
        >Previous</a
      >
    </li>
    {% endif %}

    <li class="page-item active">
      <span class="page-link">
        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
      </span>
    </li>

    {% if page_obj.has_next %}
    <li class="page-item">
      <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
    </li>
    <li class="page-item">
      <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}"
        >Last</a
      >
    </li>
    {% endif %}
  </ul>
</nav>
{% endif %} {% else %}
<div class="text-center py-5">
  <div class="mb-4">
    <i
      class="bi bi-chat-square-text"
      style="font-size: 4rem; color: var(--bs-secondary)"
    ></i>
  </div>
  <h4>No Reviews Yet</h4>
  <p class="text-muted">Be the first to submit a review!</p>
  <a href="{% url 'reviews:review_create' %}" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Add First Review
  </a>
</div>
{% endif %} {% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
<style>
  .verified-badge {
    background-color: var(--bs-success);
    color: white;
  }
  .fake-badge {
    background-color: var(--bs-danger);
    color: white;
  }
  .real-badge {
    background-color: var(--bs-success);
    color: white;
  }
</style>
{% endblock %}
