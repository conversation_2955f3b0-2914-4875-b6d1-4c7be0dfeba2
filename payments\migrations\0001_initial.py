# Generated by Django 4.2.7 on 2025-06-27 00:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('reviews', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.Char<PERSON>ield(default='GHS', max_length=3)),
                ('reference', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('verified', models.BooleanField(default=False)),
                ('paystack_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('paystack_transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('review', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='reviews.newreview')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['review_id'], name='payments_pa_review__7f0d68_idx'), models.Index(fields=['status'], name='payments_pa_status_7ad4af_idx')],
            },
        ),
    ]
