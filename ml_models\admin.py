from django.contrib import admin
from .models import MLModel

@admin.register(MLModel)
class MLModelAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'version', 'accuracy', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'version', 'description')
    readonly_fields = ('id', 'created_at', 'updated_at')
    fieldsets = (
        ('Model Information', {
            'fields': ('id', 'name', 'version', 'description', 'file_path')
        }),
        ('Performance Metrics', {
            'fields': ('accuracy', 'precision', 'recall', 'f1_score')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    actions = ['activate_model']
    
    def activate_model(self, request, queryset):
        """
        Action to activate a model and deactivate all others
        """
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one model to activate")
            return
        
        # Deactivate all models
        self.model.objects.all().update(is_active=False)
        
        # Activate selected model
        model = queryset.first()
        model.is_active = True
        model.save()
        
        self.message_user(request, f"Model '{model.name} v{model.version}' has been activated")
    
    activate_model.short_description = "Activate selected model (and deactivate others)"
