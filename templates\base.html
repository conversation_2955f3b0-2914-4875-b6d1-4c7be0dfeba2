<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TTUGuard - Fake Review Detection System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        
        .fake-badge {
            background-color: #dc3545;
            color: white;
        }
        
        .real-badge {
            background-color: #198754;
            color: white;
        }
        
        .verified-badge {
            background-color: #0d6efd;
            color: white;
        }
        
        /* Star rating styles for display */
        .star-display {
            color: #ffc107;
        }
        
        .star-empty {
            color: #e4e5e9;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
        <div class="container">
            <a class="navbar-brand" href="{% url 'reviews:review_list' %}">TTUGuard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reviews:review_list' %}">Reviews</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reviews:review_create' %}">Submit Review</a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reviews:admin_dashboard' %}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reviews:scrape_reviews' %}">Scrape Reviews</a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:index' %}">Admin</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:logout' %}">Logout</a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:login' %}">Login</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mb-4">
        <!-- Messages -->
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Page Header -->
        <h1 class="mb-4">{% block page_title %}{% endblock %}</h1>
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-auto">
        <div class="container">
            <p class="text-center text-muted mb-0">
                &copy; {% now "Y" %} TTUGuard - Fake Review Detection System
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}
</body>
</html> 