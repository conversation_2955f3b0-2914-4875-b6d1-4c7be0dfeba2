<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}ReviewMate - Fake Review Detection System{% endblock %}
    </title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}

    <style>
      :root {
        --primary-color: #6366f1;
        --primary-dark: #4f46e5;
        --secondary-color: #10b981;
        --danger-color: #ef4444;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
      }

      .navbar-brand {
        font-weight: bold;
        font-size: 1.5rem;
        color: var(--primary-color) !important;
      }

      .navbar-dark {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        ) !important;
      }

      .fake-badge {
        background-color: var(--danger-color);
        color: white;
        border-radius: 12px;
        padding: 0.4rem 0.8rem;
      }

      .real-badge {
        background-color: var(--secondary-color);
        color: white;
        border-radius: 12px;
        padding: 0.4rem 0.8rem;
      }

      .verified-badge {
        background-color: var(--info-color);
        color: white;
        border-radius: 12px;
        padding: 0.4rem 0.8rem;
      }

      /* Star rating styles for display */
      .star-display {
        color: #fbbf24;
      }

      .star-empty {
        color: #e5e7eb;
      }

      .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }

      .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
      }

      .card {
        border-radius: 12px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: none;
      }

      .card-header {
        background-color: var(--light-color);
        border-bottom: 1px solid #e5e7eb;
        border-radius: 12px 12px 0 0 !important;
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
      <div class="container">
        <a class="navbar-brand" href="{% url 'reviews:review_list' %}"
          >ReviewMate</a
        >
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <a class="nav-link" href="{% url 'reviews:review_list' %}"
                >Reviews</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'reviews:review_create' %}"
                >Submit Review</a
              >
            </li>
            {% if user.is_staff %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'reviews:admin_dashboard' %}"
                >Dashboard</a
              >
            </li>
            {% endif %}
          </ul>
          <ul class="navbar-nav">
            {% if user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'admin:index' %}">Admin</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'admin:logout' %}">Logout</a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{% url 'admin:login' %}">Login</a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="container mb-4">
      <!-- Messages -->
      {% if messages %}
      <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Page Header -->
      <h1 class="mb-4">{% block page_title %}{% endblock %}</h1>

      <!-- Page Content -->
      {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-auto">
      <div class="container">
        <p class="text-center text-muted mb-0">
          &copy; {% now "Y" %} ReviewMate - Fake Review Detection System
        </p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}
  </body>
</html>
