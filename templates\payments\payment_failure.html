{% extends 'base.html' %}

{% block title %}Payment Failed - ReviewMate{% endblock %}

{% block page_title %}Payment Failed{% endblock %}

{% block content %}
<div class="row">
  <div class="col-md-8 offset-md-2">
    <div class="card">
      <div class="card-header bg-danger text-white">
        <h5 class="mb-0">Payment Failed</h5>
      </div>
      <div class="card-body">
        <div class="text-center mb-4">
          <i
            class="bi bi-exclamation-circle-fill text-danger"
            style="font-size: 4rem"
          ></i>
        </div>

        <div class="alert alert-danger">
          <p>
            Your payment could not be processed. Please try again or contact
            support if the issue persists.
          </p>
          <p>Reference: <strong>{{ payment.reference }}</strong></p>
        </div>

        <div class="mb-4">
          <h6>Review Details</h6>
          <div class="card bg-light p-3">
            <p><strong>Product:</strong> {{ review.product_name }}</p>
            <p><strong>Rating:</strong> {{ review.rating }} / 5</p>
            <p>
              <strong>Status:</strong>
              <span class="badge bg-danger">Not Verified</span>
            </p>
          </div>
        </div>

        <div class="d-grid gap-2">
          <a
            href="{% url 'payments:payment_page' review_id=review.id %}"
            class="btn btn-primary"
          >
            Try Again
          </a>
          <a
            href="{% url 'reviews:review_detail' pk=review.id %}"
            class="btn btn-outline-secondary"
          >
            Back to Review
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
/>
{% endblock %}
