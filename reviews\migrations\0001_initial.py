# Generated by Django 4.2.7 on 2025-06-27 00:51

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='NewReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_id', models.UUIDField(blank=True, null=True)),
                ('product_id', models.CharField(max_length=255)),
                ('product_name', models.Char<PERSON>ield(max_length=255)),
                ('review_text', models.TextField()),
                ('rating', models.IntegerField()),
                ('author', models.Char<PERSON>ield(max_length=255)),
                ('date_posted', models.DateTimeField(auto_now_add=True)),
                ('source', models.CharField(default='user', max_length=100)),
                ('prediction', models.CharField(blank=True, choices=[('real', 'Real'), ('fake', 'Fake')], max_length=10, null=True)),
                ('confidence', models.FloatField(blank=True, null=True)),
                ('is_flagged', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-date_posted'],
                'indexes': [models.Index(fields=['product_id'], name='reviews_new_product_9f4551_idx'), models.Index(fields=['is_flagged'], name='reviews_new_is_flag_422614_idx'), models.Index(fields=['is_verified'], name='reviews_new_is_veri_13dcc5_idx'), models.Index(fields=['prediction'], name='reviews_new_predict_5703c4_idx')],
            },
        ),
    ]
