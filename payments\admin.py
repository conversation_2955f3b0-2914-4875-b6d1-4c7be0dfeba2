from django.contrib import admin
from .models import Payment

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('id', 'review_id', 'amount', 'currency', 'status', 'created_at')
    list_filter = ('status', 'currency')
    search_fields = ('review_id', 'razorpay_payment_id', 'razorpay_order_id')
    readonly_fields = ('id', 'created_at', 'updated_at')
    fieldsets = (
        ('Payment Information', {
            'fields': ('id', 'review_id', 'amount', 'currency', 'status')
        }),
        ('Razorpay Details', {
            'fields': ('razorpay_payment_id', 'razorpay_order_id', 'razorpay_signature')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )
